# KVrocks configuration file
# This file is based on the default KVrocks configuration

################################ GENERAL #####################################

# By default kvrocks listens for connections from localhost interface.
# It is possible to listen to just one or multiple interfaces using
# the "bind" configuration directive, followed by one or more IP addresses.
bind 0.0.0.0

# Accept connections on the specified port, default is 6666.
port 6666

# Close the connection after a client is idle for N seconds (0 to disable)
timeout 0

# The number of worker's threads, increase or decrease would affect the performance.
workers 4

# By default, kvrocks does not run as a daemon. Use 'yes' if you need it.
daemonize no

# Cluster mode configuration
cluster-enabled no

# Set the max number of connected clients at the same time.
maxclients 1024

# Master-Slave replication would check db name is matched.
db-name kvrocks.db

# The working directory
dir /var/lib/kvrocks/data

# Log level
# Possible values: debug, info, warning, error, fatal
log-level info

# You can configure log-retention-days to control whether to enable the log cleaner
log-retention-days -1

# You can configure a slave instance to accept writes or not.
slave-read-only yes

# The slave priority is an integer number published by Kvrocks in the INFO output.
slave-priority 100

# TCP listen() backlog.
tcp-backlog 511

################################## SLOW LOG ###################################

# The following time is expressed in microseconds, so 1000000 is equivalent
# to one second. Note that -1 value disables the slow log, while
# a value of zero forces the logging of every command.
slowlog-log-slower-than 100000

# There is no limit to this length. Just be aware that it will consume memory.
slowlog-max-len 128

################################ ROCKSDB #####################################

# Specify the capacity of column family block cache.
rocksdb.block_cache_size 512

# Number of open files that can be used by the DB.
rocksdb.max_open_files 8096

# Amount of data to build up in memory before converting to a sorted on-disk file.
rocksdb.write_buffer_size 64

# Target file size for compaction
rocksdb.target_file_size_base 128

# The maximum number of write buffers that are built up in memory.
rocksdb.max_write_buffer_number 4

# Maximum number of concurrent background jobs (compactions and flushes).
rocksdb.max_background_jobs 4

# WAL configuration
rocksdb.wal_ttl_seconds 10800
rocksdb.wal_size_limit_mb 16384

# Block size
rocksdb.block_size 16384

# Compression
rocksdb.compression snappy

# Enable compression from n levels of LSM-tree.
rocksdb.compression_start_level 2

# Level 0 configuration
rocksdb.level0_slowdown_writes_trigger 20
rocksdb.level0_stop_writes_trigger 40
rocksdb.level0_file_num_compaction_trigger 4

# Write options
rocksdb.write_options.sync no
rocksdb.write_options.disable_wal no

# Read options
rocksdb.read_options.async_io yes

################################ NAMESPACE #####################################
# namespace.default change.me
