additionalProperties:
  formFields:
    - default: "/home/<USER>"
      envKey: KVROCKS_ROOT_PATH
      labelZh: 数据持久化路径
      labelEn: Data persistence path
      required: true
      type: text
    - default: 6666
      envKey: PANEL_APP_PORT_HTTP
      labelZh: 端口
      labelEn: Port
      required: true
      rule: paramPort
      type: number
    - default: ""
      envKey: KVROCKS_ROOT_PASSWORD
      labelZh: 密码
      labelEn: Password
      random: true
      required: false
      rule: paramComplexity
      type: password
    - default: 4
      envKey: KVROCKS_WORKERS
      labelZh: 工作线程数
      labelEn: Worker threads
      required: false
      type: number
    - default: 1024
      envKey: KVROCKS_MAX_CLIENTS
      labelZh: 最大客户端连接数
      labelEn: Max clients
      required: false
      type: number
