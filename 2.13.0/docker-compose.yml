networks:
  1panel-network:
    external: true

services:
  kvrocks:
    image: apache/kvrocks:2.13.0
    container_name: ${CONTAINER_NAME}
    labels:
      createdBy: "Apps"
    restart: always
    networks:
      - 1panel-network
    ports:
      - ${PANEL_APP_PORT_HTTP}:6666
    command: >
      sh -c '
      if [ -z "${KVROCKS_ROOT_PASSWORD}" ]; then
        kvrocks -c /var/lib/kvrocks/kvrocks.conf --bind 0.0.0.0 --workers ${KVROCKS_WORKERS:-4} --max-clients ${KVROCKS_MAX_CLIENTS:-1024}
      else
        kvrocks -c /var/lib/kvrocks/kvrocks.conf --bind 0.0.0.0 --workers ${KVROCKS_WORKERS:-4} --max-clients ${KVROCKS_MAX_CLIENTS:-1024} --requirepass ${KVROCKS_ROOT_PASSWORD}
      fi'
    volumes:
      - ${KVROCKS_ROOT_PATH}/data:/var/lib/kvrocks/data
      - ${KVROCKS_ROOT_PATH}/config/kvrocks.conf:/var/lib/kvrocks/kvrocks.conf
      - ${KVROCKS_ROOT_PATH}/logs:/var/lib/kvrocks/logs
    environment:
      - TZ=Asia/Shanghai
