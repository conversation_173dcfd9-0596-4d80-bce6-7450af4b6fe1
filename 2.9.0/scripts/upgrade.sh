#!/bin/bash

if [ -f .env ]; then
  source .env

  echo "Upgrading KVrocks configuration..."
  
  # Backup current configuration
  if [ -f "$KVROCKS_ROOT_PATH/config/kvrocks.conf" ]; then
    cp "$KVROCKS_ROOT_PATH/config/kvrocks.conf" "$KVROCKS_ROOT_PATH/config/kvrocks.conf.backup.$(date +%Y%m%d_%H%M%S)"
  fi

  # Copy new configuration
  cp ./config/kvrocks.conf "$KVROCKS_ROOT_PATH/config/kvrocks.conf"

  echo "KVrocks upgrade completed."

else
  echo "Error: .env file not found."
fi
