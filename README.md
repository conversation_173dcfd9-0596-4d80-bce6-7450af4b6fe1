# Apache KVrocks

Apache KVrocks 是一个分布式键值 NoSQL 数据库，使用 RocksDB 作为存储引擎，并与 Redis 协议兼容。

![KVrocks](https://kvrocks.apache.org/img/logo.svg)

![](https://img.shields.io/badge/%E6%96%B0%E7%96%86%E8%90%8C%E6%A3%AE%E8%BD%AF%E4%BB%B6%E5%BC%80%E5%8F%91%E5%B7%A5%E4%BD%9C%E5%AE%A4-%E6%8F%90%E4%BE%9B%E6%8A%80%E6%9C%AF%E6%94%AF%E6%8C%81-blue)

## 简介

Apache KVrocks 是一个分布式键值 NoSQL 数据库，它使用 RocksDB 作为存储引擎，并与 Redis 协议兼容。这意味着用户可以通过任何 Redis 客户端访问 Apache KVrocks。

KVrocks 的主要特性：

+ **Redis 兼容性**: 支持大部分 Redis 命令和数据结构，可以作为 Redis 的替代方案
+ **持久化存储**: 基于 RocksDB 提供可靠的持久化存储，数据不会因为重启而丢失
+ **高性能**: 利用 RocksDB 的高性能特性，提供优秀的读写性能
+ **分布式架构**: 支持集群模式，可以水平扩展以处理更大的数据量
+ **内存效率**: 相比纯内存数据库，KVrocks 可以处理超过内存大小的数据集
+ **备份和恢复**: 提供完整的备份和恢复功能
+ **监控和运维**: 提供丰富的监控指标和运维工具

KVrocks 特别适合需要 Redis 兼容性但又需要持久化存储和更大数据容量的场景。

---

![Ms Studio](https://file.lifebus.top/imgs/ms_blank_001.png)
