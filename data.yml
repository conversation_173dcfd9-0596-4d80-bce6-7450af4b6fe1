additionalProperties:
  key: kvrocks
  name: KVrocks
  tags:
    - Database
    - Runtime
    - Storage
    - Local
  shortDescZh: 基于RocksDB的分布式键值数据库，兼容Redis协议
  shortDescEn: Distributed key-value NoSQL database using RocksDB storage engine, Redis protocol compatible
  type: runtime
  crossVersionUpdate: true
  limit: 0
  website: https://kvrocks.apache.org/
  github: https://github.com/apache/kvrocks/
  document: https://kvrocks.apache.org/docs/
